package schema

import (
	"errors"
	"fmt"
	"reflect"
	"strings"
	"sync"
)

var (
	ErrInvalidSchemaType  = errors.New("invalid schema type")
	ErrInvalidNodeType    = errors.New("invalid node type")
	ErrInvalidLeaf        = errors.New("invalid leaf")
	ErrDuplicateFieldName = errors.New("duplicate field name")
)

const (
	TagBuiltinName = "name"
	TagBuiltinNode = "node"
)

type SchemaField struct {
	v any
}

func (s SchemaField) NodeField() (n NodeField, ok bool) {
	n, ok = s.v.(NodeField)
	return
}

func (s SchemaField) LeafField() (l LeafField, ok bool) {
	l, ok = s.v.(LeafField)
	return
}

func (s SchemaField) Ok() bool {
	return s.v != nil
}

type Schema interface {
	Name() string
	Type() reflect.Type
	SchemaField(name string) (sf SchemaField)
	Node(i int) NodeField
	Leaf(i int) LeafField
	LeafLen() int
	NodeLen() int
}

type schema struct {
	name        string
	modelType   reflect.Type
	name2Field  map[string]any
	leafs       []*leafField
	nodes       []*node<PERSON>ield
	initialized chan struct{}
	err         error
}

func (s *schema) SchemaField(name string) (sf SchemaField) {
	if f, ok := s.name2Field[name]; ok {
		sf.v = f
	}
	return
}

func (s *schema) Name() string {
	return s.name
}

func (s *schema) Type() reflect.Type {
	return s.modelType
}

func (s *schema) Node(i int) NodeField {
	return s.nodes[i]
}

func (s *schema) Leaf(i int) LeafField {
	return s.leafs[i]
}

func (s *schema) LeafLen() int {
	return len(s.leafs)
}

func (s *schema) NodeLen() int {
	return len(s.nodes)
}

func parseFieldTagSetting(str string, sep string) map[string]string {
	settings := map[string]string{}
	names := strings.Split(str, sep)
	for i := 0; i < len(names); i++ {
		j := i
		if len(names[j]) > 0 {
			for {
				if names[j][len(names[j])-1] == '\\' {
					i++
					names[j] = names[j][0:len(names[j])-1] + sep + names[i]
					names[i] = ""
				} else {
					break
				}
			}
		}

		values := strings.Split(names[j], ":")
		k := strings.TrimSpace(values[0])

		if len(values) >= 2 {
			settings[k] = strings.Join(values[1:], ":")
		} else if k != "" {
			settings[k] = k
		}
	}
	return settings
}

func (s *schema) addName2Field(field Field) error {
	if _, ok := s.name2Field[field.Name()]; ok {
		return fmt.Errorf("%s: field name: %s", ErrDuplicateFieldName, field.Name())
	} else {
		s.name2Field[field.Name()] = field
		return nil
	}
}

func (s *schema) parseField(structField reflect.StructField, tagKey string, cache *sync.Map, embedded bool) error {
	if structField.Anonymous {
		return s.parseEmbeddedField(structField, cache, tagKey, embedded)
	}

	fieldTag, ok := structField.Tag.Lookup(tagKey)
	if !ok {
		return nil
	}
	tagSetting := parseFieldTagSetting(fieldTag, ";")
	if _, ok := tagSetting[TagBuiltinNode]; ok {
		return s.parseNodeField(structField, cache, tagKey, tagSetting, embedded)
	} else {
		return s.parseLeafField(structField, tagSetting, embedded)
	}
}

func (s *schema) parseEmbeddedField(structField reflect.StructField, cache *sync.Map, tagKey string, embedded bool) error {
	indirectType := structField.Type
	if indirectType.Kind() == reflect.Ptr {
		indirectType = indirectType.Elem()
	}

	switch indirectType.Kind() {
	case reflect.Struct:
		aSchema, err := parse(reflect.New(indirectType).Interface(), cache, tagKey, true, true)
		if err != nil {
			return err
		}
		for _, leaf := range aSchema.leafs {
			if structField.Type.Kind() == reflect.Struct {
				leaf.structField.Index = append([]int{structField.Index[0]}, leaf.structField.Index...)
			} else {
				leaf.structField.Index = append([]int{structField.Index[0] - 1}, leaf.structField.Index...)
			}
			if !embedded {
				leaf.setup()
			}
			s.leafs = append(s.leafs, leaf)
			if err = s.addName2Field(leaf); err != nil {
				return err
			}
		}
		for _, node := range aSchema.nodes {
			if structField.Type.Kind() == reflect.Struct {
				node.structField.Index = append([]int{structField.Index[0]}, node.structField.Index...)
			} else {
				node.structField.Index = append([]int{structField.Index[0] - 1}, node.structField.Index...)
			}
			if !embedded {
				node.setup()
			}
			s.nodes = append(s.nodes, node)
			if err = s.addName2Field(node); err != nil {
				return err
			}
		}
	default:
		return fmt.Errorf("invalid embedded struct %s should be struce or pointer to struct, but got %v", structField.Name, structField.Type)
	}
	return nil
}

func (s *schema) parseLeafField(structField reflect.StructField, tagSetting map[string]string, embedded bool) error {
	leaf, err := parseLeafField(structField, tagSetting)
	if err != nil {
		return err
	}
	if !embedded {
		leaf.setup()
	}
	s.leafs = append(s.leafs, leaf)
	return s.addName2Field(leaf)
}

func (s *schema) parseNodeField(structField reflect.StructField, cache *sync.Map, tagKey string, tagSetting map[string]string,
	embedded bool) error {
	node, err := parseNodeField(structField, cache, tagKey, tagSetting)
	if err != nil {
		return err
	}
	if !embedded {
		node.setup()
	}
	s.nodes = append(s.nodes, node)
	return s.addName2Field(node)
}

func Parse(dest any, cache *sync.Map, tagKey string) (Schema, error) {
	return parse(dest, cache, tagKey, false, false)
}

func parse(dest any, cache *sync.Map, tagKey string, nested bool, embedded bool) (*schema, error) {
	if dest == nil {
		return nil, fmt.Errorf("%w: dest is a nil", ErrInvalidSchemaType)
	}

	value := reflect.ValueOf(dest)
	if value.Kind() == reflect.Ptr && value.IsNil() {
		value = reflect.New(value.Type().Elem())
	}
	modelType := reflect.Indirect(value).Type()

	if modelType.Kind() == reflect.Interface {
		modelType = reflect.Indirect(reflect.ValueOf(dest)).Elem().Type()
	}
	for modelType.Kind() == reflect.Ptr || modelType.Kind() == reflect.Map ||
		modelType.Kind() == reflect.Slice || modelType.Kind() == reflect.Array {
		modelType = modelType.Elem()
	}
	if modelType.Kind() != reflect.Struct {
		return nil, fmt.Errorf("%w: dest indirect type must be a struct", ErrInvalidSchemaType)
	}
	aSchema := &schema{
		name:        modelType.String(),
		modelType:   modelType,
		name2Field:  map[string]any{},
		leafs:       []*leafField{},
		nodes:       []*nodeField{},
		initialized: make(chan struct{}),
	}
	if v, ok := cache.LoadOrStore(modelType, aSchema); ok {
		s := v.(*schema)
		if !nested {
			<-s.initialized
		}
		return s, s.err
	}
	defer func() {
		if aSchema.err != nil {
			cache.Delete(modelType)
		}
		close(aSchema.initialized)
	}()
	if !nested {
		cache = &sync.Map{}
		cache.Store(modelType, aSchema)
	}
	for i := 0; i < modelType.NumField(); i++ {
		aSchema.err = aSchema.parseField(modelType.Field(i), tagKey, cache, embedded)
		if aSchema.err != nil {
			return aSchema, aSchema.err
		}
	}
	return aSchema, aSchema.err
}
