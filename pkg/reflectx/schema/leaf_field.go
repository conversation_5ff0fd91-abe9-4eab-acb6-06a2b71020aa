package schema

import (
	"fmt"
	"go/ast"
	"reflect"
	"strconv"
)

type LeafField interface {
	Field
	SetStructValue(structValue reflect.Value, value any) error
}

type leafField struct {
	*field
	set func(reflect.Value, any) error
}

func (f *leafField) SetStructValue(structValue reflect.Value, value any) error {
	return f.set(structValue, value)
}

func parseLeafField(structField reflect.StructField, tagSetting map[string]string) (*leafField, error) {
	if !ast.IsExported(structField.Name) {
		return nil, fmt.Errorf("%w: leaf field must be export", ErrInvalidLeaf)
	}
	f := &leafField{
		field: parseField(structField, tagSetting),
	}
	return f, nil
}

func (f *leafField) setupSet() {
	fallbackSetter := func(value reflect.Value, v any, setter func(reflect.Value, any) error) (err error) {
		if v == nil {
			f.reflectValueOf(value).Set(reflect.New(f.fieldType).Elem())
		} else {
			reflectV := reflect.ValueOf(v)
			// Optimal value type acquisition for v
			reflectValType := reflectV.Type()

			if reflectValType.AssignableTo(f.fieldType) {
				f.reflectValueOf(value).Set(reflectV)
				return
			} else if reflectValType.ConvertibleTo(f.fieldType) {
				f.reflectValueOf(value).Set(reflectV.Convert(f.fieldType))
				return
			} else if f.fieldType.Kind() == reflect.Ptr {
				fieldValue := f.reflectValueOf(value)
				fieldType := f.fieldType.Elem()

				if reflectValType.AssignableTo(fieldType) {
					if !fieldValue.IsValid() {
						fieldValue = reflect.New(fieldType)
					} else if fieldValue.IsNil() {
						fieldValue.Set(reflect.New(fieldType))
					}
					fieldValue.Elem().Set(reflectV)
					return
				} else if reflectValType.ConvertibleTo(fieldType) {
					if fieldValue.IsNil() {
						fieldValue.Set(reflect.New(fieldType))
					}

					fieldValue.Elem().Set(reflectV.Convert(fieldType))
					return
				}
			}
			if reflectV.Kind() == reflect.Ptr {
				if reflectV.IsNil() {
					f.reflectValueOf(value).Set(reflect.New(f.fieldType).Elem())
				} else if reflectV.Type().Elem().AssignableTo(f.fieldType) {
					f.reflectValueOf(value).Set(reflectV.Elem())
					return
				} else {
					err = setter(value, reflectV.Elem().Interface())
				}
			} else {
				return fmt.Errorf("failed to set value to field : %s", f.name)
			}
		}
		return
	}

	switch f.fieldType.Kind() {
	case reflect.Bool:
		f.set = func(value reflect.Value, v any) error {
			switch data := v.(type) {
			case **bool:
				if data != nil && *data != nil {
					f.reflectValueOf(value).SetBool(**data)
				}
			case bool:
				f.reflectValueOf(value).SetBool(data)
			case uint, uint8, uint16, uint32, uint64:
				f.reflectValueOf(value).SetBool(reflect.ValueOf(data).Convert(reflect.TypeOf(uint64(1))).Uint() > 0)
			case int, int8, int16, int32, int64:
				f.reflectValueOf(value).SetBool(reflect.ValueOf(data).Convert(reflect.TypeOf(int64(1))).Int() > 0)
			case string:
				b, _ := strconv.ParseBool(data)
				f.reflectValueOf(value).SetBool(b)
			default:
				return fallbackSetter(value, v, f.set)
			}
			return nil
		}
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		f.set = func(value reflect.Value, v any) (err error) {
			switch data := v.(type) {
			case **int64:
				if data != nil && *data != nil {
					f.reflectValueOf(value).SetInt(**data)
				}
			case int:
				f.reflectValueOf(value).SetInt(int64(data))
			case int8:
				f.reflectValueOf(value).SetInt(int64(data))
			case int16:
				f.reflectValueOf(value).SetInt(int64(data))
			case int32:
				f.reflectValueOf(value).SetInt(int64(data))
			case int64:
				f.reflectValueOf(value).SetInt(int64(data))
			case uint:
				f.reflectValueOf(value).SetInt(int64(data))
			case uint8:
				f.reflectValueOf(value).SetInt(int64(data))
			case uint16:
				f.reflectValueOf(value).SetInt(int64(data))
			case uint32:
				f.reflectValueOf(value).SetInt(int64(data))
			case uint64:
				f.reflectValueOf(value).SetInt(int64(data))
			case float32:
				f.reflectValueOf(value).SetInt(int64(data))
			case float64:
				f.reflectValueOf(value).SetInt(int64(data))
			case []byte:
				return f.set(value, string(data))
			case string:
				if i, err := strconv.ParseInt(data, 0, 64); err == nil {
					f.reflectValueOf(value).SetInt(i)
				} else {
					return err
				}
			default:
				return fallbackSetter(value, v, f.set)
			}
			return err
		}
	case reflect.String:
		f.set = func(value reflect.Value, v any) (err error) {
			switch data := v.(type) {
			case **string:
				if data != nil && *data != nil {
					f.reflectValueOf(value).SetString(**data)
				}
			case string:
				f.reflectValueOf(value).SetString(data)
			case []byte:
				f.reflectValueOf(value).SetString(string(data))
			case int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64:
				f.reflectValueOf(value).SetString(fmt.Sprintf("%.f", data))
			default:
				return fallbackSetter(value, v, f.set)
			}
			return err
		}
	default:
		f.set = func(value reflect.Value, v any) error {
			return fallbackSetter(value, v, f.set)
		}
	}

}

func (f *leafField) setup() {
	f.field.setup()
	f.setupSet()
}
