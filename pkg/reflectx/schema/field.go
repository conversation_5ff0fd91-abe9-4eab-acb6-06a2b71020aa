package schema

import "reflect"

type Field interface {
	ValueOf(structValue reflect.Value) (val any, zero bool)
	ReflectValueOf(structValue reflect.Value) reflect.Value
	Name() string
	FieldType() reflect.Type
	StructTag() reflect.StructTag
	Tag(key string) (string, bool)
	Index() []int
}
type field struct {
	name              string
	indirectFieldType reflect.Type
	fieldType         reflect.Type
	structField       reflect.StructField
	structTag         reflect.StructTag
	tagSetting        map[string]string
	reflectValueOf    func(reflect.Value) reflect.Value
	valueOf           func(reflect.Value) (val any, zero bool)
}

func (f *field) ValueOf(structValue reflect.Value) (val any, zero bool) {
	return f.valueOf(structValue)
}

func (f *field) ReflectValueOf(structValue reflect.Value) reflect.Value {
	return f.reflectValueOf(structValue)
}

func (f *field) Name() string {
	return f.name
}

func (f *field) FieldType() reflect.Type {
	return f.fieldType
}

func (f *field) StructTag() reflect.StructTag {
	return f.structTag
}

func (f *field) Tag(key string) (string, bool) {
	tag, ok := f.tagSetting[key]
	return tag, ok
}

func (f *field) Index() []int {
	return f.structField.Index
}

func parseField(structField reflect.StructField, tagSetting map[string]string) *field {
	f := &field{
		structField: structField,
		tagSetting:  tagSetting,
	}
	if name, ok := f.tagSetting[TagBuiltinName]; ok {
		f.name = name
	} else {
		f.name = f.structField.Name
	}
	f.fieldType = f.structField.Type
	f.structTag = f.structField.Tag
	f.indirectFieldType = f.structField.Type
	for f.indirectFieldType.Kind() == reflect.Ptr {
		f.indirectFieldType = f.indirectFieldType.Elem()
	}
	return f
}

func (f *field) setup() {
	f.setupValueOf()
	f.setupReflectValueOf()
}

func (f *field) setupValueOf() {
	fieldIndex := f.structField.Index[0]
	switch {
	case len(f.structField.Index) == 1 && fieldIndex > 0:
		f.valueOf = func(value reflect.Value) (any, bool) {
			v := reflect.Indirect(value).Field(fieldIndex)
			return v.Interface(), v.IsZero()
		}
	default:
		f.valueOf = func(v reflect.Value) (val any, zero bool) {
			v = reflect.Indirect(v)
			for _, fieldIdx := range f.structField.Index {
				if fieldIdx >= 0 {
					v = v.Field(fieldIdx)
				} else {
					v = v.Field(-fieldIdx - 1)
					if !v.IsNil() {
						v = v.Elem()
					} else {
						return nil, true
					}
				}
			}
			fv, zero := v.Interface(), v.IsZero()
			return fv, zero
		}
	}
}

func (f *field) setupReflectValueOf() {
	fieldIndex := f.structField.Index[0]
	switch {
	case len(f.structField.Index) == 1 && fieldIndex > 0:
		f.reflectValueOf = func(v reflect.Value) reflect.Value {
			return reflect.Indirect(v).Field(fieldIndex)
		}
	default:
		f.reflectValueOf = func(v reflect.Value) reflect.Value {
			v = reflect.Indirect(v)
			for idx, fieldIdx := range f.structField.Index {
				if fieldIdx >= 0 {
					v = v.Field(fieldIdx)
				} else {
					v = v.Field(fieldIdx)

					if v.IsNil() {
						v.Set(reflect.New(v.Type().Elem()))
					}

					if idx < len(f.structField.Index)-1 {
						v = v.Elem()
					}
				}
			}
			return v
		}
	}
}
