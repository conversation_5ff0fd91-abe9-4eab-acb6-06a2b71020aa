package schema

import (
	"fmt"
	"reflect"
	"sync"
)

type NodeField interface {
	Field
	Schema() Schema
}

type nodeField struct {
	*field
	schema *schema
}

func (n *nodeField) Schema() Schema {
	return n.schema
}

func parseNodeField(structField reflect.StructField, cache *sync.Map, tagKey string, tagSetting map[string]string) (n *nodeField,
	err error) {
	n = &nodeField{
		field: parseField(structField, tagSetting),
	}
	// 这里的array可能是别的东西
	if n.indirectFieldType.Kind() == reflect.Map || n.indirectFieldType.Kind() == reflect.Slice || n.indirectFieldType.Kind() == reflect.Array {
		n.indirectFieldType = n.indirectFieldType.Elem()
	}
	for n.indirectFieldType.Kind() == reflect.Ptr {
		n.indirectFieldType = n.indirectFieldType.Elem()
	}
	if n.indirectFieldType.Kind() != reflect.Struct {
		return n, fmt.Erro<PERSON>("%w: nodes indirect filed must be struct", ErrInvalidNodeType)
	}
	n.schema, err = parse(reflect.New(n.indirectFieldType).Interface(), cache, tagKey, true, false)
	return
}
