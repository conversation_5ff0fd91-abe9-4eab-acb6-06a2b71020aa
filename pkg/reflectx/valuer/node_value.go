package valuer

import (
	"reflect"

	"peanut/pkg/reflectx/schema"
)

type FieldValue struct {
	v any
}

func (f FieldValue) NodeScroll() (n NodeScroll, ok bool) {
	n, ok = f.v.(NodeScroll)
	return
}

func (f FieldValue) LeafValue() (l LeafValue, ok bool) {
	l, ok = f.v.(LeafValue)
	return
}

func (f FieldValue) Ok() bool {
	return f.v != nil
}

type NodeValue interface {
	schema.Schema
	Value() reflect.Value
	Interface() (any, bool)
	FieldValue(name string) (fv FieldValue)
	LeafValue(i int) LeafValue
	NodeScroll(i int) NodeScroll
}

type nodeValue struct {
	schema.Schema
	value         reflect.Value
	indirectValue reflect.Value
	name2Value    map[string]any
	leafValues    []*leafValue
	nodeScrolls   []*nodeScroll
}

func (n *nodeValue) Value() reflect.Value {
	return n.value
}

func (n *nodeValue) Interface() (any, bool) {
	return n.value.Interface(), n.value.IsZero()
}

func (n *nodeValue) LeafValue(i int) LeafValue {
	return n.leafValues[i]
}

func (n *nodeValue) FieldValue(name string) (fv FieldValue) {
	if v, ok := n.name2Value[name]; ok {
		fv.v = v
	}
	return
}

func (n *nodeValue) NodeScroll(i int) NodeScroll {
	return n.nodeScrolls[i]
}

func parseNodeValue(aSchema schema.Schema, value reflect.Value, op *option) *nodeValue {
	n := &nodeValue{
		Schema:        aSchema,
		value:         value,
		indirectValue: value,
		name2Value:    map[string]any{},
	}
	for n.indirectValue.Kind() == reflect.Ptr {
		if n.indirectValue.IsNil() {
			return n
		}
	}
	for i := 0; i < n.LeafLen(); i++ {
		leaf := n.Leaf(i)
		aLeafValue := parseLeafValue(leaf, n.indirectValue, op)
		n.leafValues = append(n.leafValues, aLeafValue)
		n.name2Value[aLeafValue.Name()] = aLeafValue
	}
	for i := 0; i < n.NodeLen(); i++ {
		node := n.Node(i)
		aNodeScroll := parseNodeScroll(node, node.ReflectValueOf(n.indirectValue), op)
		n.nodeScrolls = append(n.nodeScrolls, aNodeScroll)
		n.name2Value[aNodeScroll.Name()] = aNodeScroll
	}
	return n
}
