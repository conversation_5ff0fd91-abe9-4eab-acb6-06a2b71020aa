package valuer

import (
	"reflect"

	"peanut/pkg/reflectx/schema"
)

type LeafValue interface {
	schema.LeafField
	StructValue() reflect.Value
	Set(v any) error
	Value() reflect.Value
	Interface() (value any, zero bool)
}

type leafValue struct {
	schema.LeafField
	structValue reflect.Value
}

func parseLeafValue(leafField schema.LeafField, structValue reflect.Value, op *option) *leafValue {
	return &leafValue{
		LeafField:   leafField,
		structValue: structValue,
	}
}

func (f *leafValue) StructValue() reflect.Value {
	return f.structValue
}

func (f *leafValue) Set(v any) error {
	return f.LeafField.SetStructValue(f.structValue, v)
}

func (f *leafValue) Value() reflect.Value {
	return f.LeafField.ReflectValueOf(f.structValue)
}

func (f *leafValue) Interface() (value any, zero bool) {
	return f.LeafField.ValueOf(f.structValue)
}
