package valuer

import (
	"reflect"

	"peanut/pkg/reflectx/schema"
)

type NodeScroll interface {
	Scroll
	NodeField() schema.NodeField
}

type nodeScroll struct {
	*scroll
	nodeField schema.NodeField
}

func parseNodeScroll(nodeField schema.NodeField, value reflect.Value, op *option) *nodeScroll {
	return &nodeScroll{
		scroll:    parseScroll(nodeField.Schema(), value, op),
		nodeField: nodeField,
	}
}

func (n *nodeScroll) NodeField() schema.NodeField {
	return n.nodeField
}
