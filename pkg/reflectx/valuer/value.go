package valuer

import (
	"reflect"
	"sync"

	"peanut/pkg/reflectx/schema"
)

type Value interface {
	schema.Schema
	Scroll() Scroll
}

type value struct {
	schema.Schema
	scroll *scroll
}

func Parse(dest any, cache *sync.Map, tagKey string, ops ...Option) (Value, error) {
	aSchema, err := schema.Parse(dest, cache, tagKey)
	if err != nil {
		return nil, err
	}
	op := buildOption(ops...)
	return &value{
		Schema: aSchema,
		scroll: parseScroll(aSchema, reflect.ValueOf(dest), op),
	}, nil
}

func (v *value) Scroll() Scroll {
	return v.scroll
}
