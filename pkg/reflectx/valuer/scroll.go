package valuer

import (
	"reflect"

	"peanut/pkg/reflectx/schema"
)

type Scroll interface {
	schema.Schema
	Value() reflect.Value
	Interface() (any, bool)
	NodeValue(i int) NodeValue
	NodeValueLen() int
}

type scroll struct {
	schema.Schema
	value         reflect.Value
	indirectValue reflect.Value
	nodeValues    []*nodeValue
}

func parseScroll(aSchema schema.Schema, value reflect.Value, op *option) *scroll {
	s := &scroll{
		Schema: aSchema,
		value:  value,
	}
	s.indirectValue = s.value
	for s.indirectValue.Kind() == reflect.Ptr || s.indirectValue.Kind() == reflect.Interface {
		if s.indirectValue.IsNil() {
			if op.fillIfEmpty {
				s.indirectValue.Set(reflect.New(s.indirectValue.Type().Elem()))
			} else {
				return s
			}
		}
		s.indirectValue = s.indirectValue.Elem()
	}
	if s.indirectValue.Kind() == reflect.Slice || s.indirectValue.Kind() == reflect.Array {
		for i := 0; i < s.indirectValue.Len(); i++ {
			aNodeValue := parseNodeValue(aSchema, s.indirectValue.Index(i), op)
			s.nodeValues = append(s.nodeValues, aNodeValue)
		}
	} else if s.indirectValue.Kind() == reflect.Map {
		mapIter := s.indirectValue.MapRange()
		for mapIter.Next() {
			aNodeValue := parseNodeValue(aSchema, mapIter.Value(), op)
			s.nodeValues = append(s.nodeValues, aNodeValue)
		}
	} else if s.indirectValue.Kind() == reflect.Struct {
		aNodeValue := parseNodeValue(aSchema, s.indirectValue, op)
		s.nodeValues = append(s.nodeValues, aNodeValue)
	}
	return s
}

func (s *scroll) Value() reflect.Value {
	return s.value
}

func (s *scroll) Interface() (any, bool) {
	return s.value.Interface(), s.value.IsZero()
}

func (s *scroll) NodeValue(i int) NodeValue {
	return s.nodeValues[i]
}

func (s *scroll) NodeValueLen() int {
	return len(s.nodeValues)
}
