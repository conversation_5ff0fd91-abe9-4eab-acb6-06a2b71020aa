package mapper

import (
	"context"
	"errors"
	"fmt"
	"reflect"
)

type mapPipeline struct {
	cycle            MapLifeCycle
	from2To2MapFunc  map[string]map[string]*mapFunc
	mapPairs         []*MapPair
	from2To2MapPairs map[string]map[string][]*MapPair
}

func newMapPipeline(cycle MapLifeCycle) *mapPipeline {
	pipeline := &mapPipeline{
		cycle: cycle,
	}
	pipeline.setup()
	return pipeline
}

func (m *mapPipeline) setup() {
	m.from2To2MapFunc = map[string]map[string]*mapFunc{}
	rv := reflect.ValueOf(m.cycle)
	rt := rv.Type()
	for i := range rt.NumMethod() {
		methodName := rt.Method(i).Name
		if matches := regexpMapName.FindStringSubmatch(methodName); matches != nil {
			fromType := matches[1]
			toType := matches[2]
			m.addMapFunc(fromType, toType, rv.Method(i))
		}
	}
}

func (m *mapPipeline) getMapFunc(from string, to string) (mapFunc *mapFunc, err error) {
	if to2MapFunc, ok := m.from2To2MapFunc[from]; ok {
		if mapFunc, ok = to2MapFunc[to]; ok {
			return
		}
	}
	err = fmt.Errorf("%s: from :%s, to: %s", ErrAbsenceOfFunc, from, to)
	return
}

func (m *mapPipeline) addMapFunc(from string, to string, fnRV reflect.Value) {
	var (
		to2MapFunc map[string]*mapFunc
		ok         bool
	)
	fnType := fnRV.Type()
	if (fnType.NumIn() != 3) ||
		!fnType.In(0).AssignableTo(ctxType) ||
		!fnType.In(1).AssignableTo(mapPairsType) ||
		fnType.In(2).Kind() != reflect.Slice ||
		(fnType.NumOut() != 3 ||
			!fnType.Out(0).AssignableTo(ctxType) ||
			fnType.Out(1).Kind() != reflect.Slice ||
			!fnType.Out(2).AssignableTo(errType)) {
		panic(fmt.Errorf("%s: %w", ErrInvalidMapFunc, errors.New("MapFunc must be a func")))

	}
	if to2MapFunc, ok = m.from2To2MapFunc[from]; !ok {
		to2MapFunc = map[string]*mapFunc{}
		m.from2To2MapFunc[from] = to2MapFunc
	}
	to2MapFunc[to] = &mapFunc{
		Fn:       fnRV,
		FromType: fnType.In(2),
	}
}

func (m *mapPipeline) AddMapPair(pair *MapPair) {
	m.mapPairs = append(m.mapPairs, pair)
}

func (m *mapPipeline) buildFrom2To2MapPairs() {
	var (
		to2MapPairs map[string][]*MapPair
		ok          bool
	)
	m.from2To2MapPairs = map[string]map[string][]*MapPair{}
	for _, pair := range m.mapPairs {
		if to2MapPairs, ok = m.from2To2MapPairs[pair.from.Type()]; !ok {
			to2MapPairs = map[string][]*MapPair{}
			m.from2To2MapPairs[pair.from.Type()] = to2MapPairs
		}
		to2MapPairs[pair.to.Type()] = append(to2MapPairs[pair.to.Type()], pair)
	}
}

func (m *mapPipeline) Fire(ctx context.Context) (rctx context.Context, err error) {
	rctx = ctx
	rctx, err = m.pre(rctx)
	if err != nil {
		return
	}
	m.buildFrom2To2MapPairs()
	rctx, err = m.mapping(rctx)
	if err != nil {
		return
	}
	rctx, err = m.after(rctx)
	return
}

func (m *mapPipeline) pre(ctx context.Context) (rctx context.Context, err error) {
	rctx = ctx
	rctx, m.mapPairs, err = m.cycle.Pre(rctx, m.mapPairs)
	if err != nil {
		return
	}
	return
}

func (m *mapPipeline) mapping(ctx context.Context) (rctx context.Context, err error) {
	var (
		aMapFunc *mapFunc
		out      []any
	)
	rctx = ctx
	for from, to2MapPairs := range m.from2To2MapPairs {
		for to, mapPairs := range to2MapPairs {
			if aMapFunc, err = m.getMapFunc(from, to); err != nil {
				return
			}
			in := make([]any, len(mapPairs))
			for i, mapPair := range mapPairs {
				in[i] = mapPair.from.Value().Interface()
			}
			rctx, out, err = aMapFunc.Call(rctx, mapPairs, in)
			if err != nil {
				return
			}
			for i, mapPair := range mapPairs {
				mapPair.to.Value().Set(reflect.ValueOf(out[i]))
			}
		}
	}
	return
}

func (m *mapPipeline) after(ctx context.Context) (rctx context.Context, err error) {
	rctx, err = m.cycle.After(ctx, m.mapPairs)
	return
}
