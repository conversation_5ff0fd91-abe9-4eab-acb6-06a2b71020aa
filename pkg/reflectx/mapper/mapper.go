package mapper

import (
	"context"
	"errors"
	"fmt"
	"reflect"
	"regexp"
	"sync"

	"peanut/pkg/reflectx/valuer"
)

var (
	regexpMapName = regexp.MustCompile(`^Map_(?P<from>[^_]+)_2_(?P<to>[^_]+)$`)
)

var (
	ctxType      = reflect.TypeOf(new(context.Context)).Elem()
	errType      = reflect.TypeOf(new(error)).Elem()
	mapPairsType = reflect.TypeOf(new([]*MapPair)).Elem()
)

const (
	TagBuiltinType    = "type"
	TagBuiltinFlat    = "flat"
	TagBuiltinCascade = "cascade"
)

var (
	ErrInvalidTo              = errors.New("invalid toPtr. to Ptr must be a point of slice|struct")
	ErrInvalidFromAndToLength = errors.New("length of from and to must be same.")
	ErrInvaLidFlatFieldType   = errors.New("invalid flat field type, type must be slice.")
	ErrAbsenceOfType          = errors.New("absence of type.")
	ErrAbsenceOfFunc          = errors.New("absence of func.")
	ErrInvalidMapFunc         = errors.New("MapFunc must be a func")
	ErrInvalidMapFuncResult   = errors.New("invalid map func result")
)

type Mapper interface {
	Map(ctx context.Context, cycle MapLifeCycle, fromSlice any, toSlicePtr any) (rctx context.Context, err error)
}

type mapFunc struct {
	Fn       reflect.Value
	FromType reflect.Type
}

func (m *mapFunc) Call(ctx context.Context, mapPairs []*MapPair, in []any) (rctx context.Context, out []any,
	err error) {
	var ok bool
	rctx = ctx
	inSliceRV := reflect.MakeSlice(m.FromType, len(in), len(in))
	for i := 0; i < len(in); i++ {
		inSliceRV.Index(i).Set(reflect.ValueOf(in[i]))
	}
	rs := m.Fn.Call([]reflect.Value{reflect.ValueOf(rctx), reflect.ValueOf(mapPairs), inSliceRV})
	if err, ok = rs[2].Interface().(error); ok {
		return
	}
	if rctx, ok = rs[0].Interface().(context.Context); !ok {
		panic(fmt.Errorf("%s: return context can not be nil", ErrInvalidMapFuncResult))
	}
	outRV := rs[1]
	if outRV.Len() != len(in) {
		panic(fmt.Errorf("%s: count dismatch , in: %d, out: %d", ErrInvalidMapFuncResult, len(in), outRV.Len()))
	}
	out = make([]any, outRV.Len())
	for i := 0; i < len(out); i++ {
		out[i] = outRV.Index(i).Interface()
	}
	return
}

type mapper struct {
	cache  *sync.Map
	tagKey string
}

func (m *mapper) Map(ctx context.Context, cycle MapLifeCycle, from any, toPtr any) (rctx context.Context, err error) {
	pipline := newMapPipeline(cycle)
	if err = m.buildPipeline(pipline, from, toPtr); err != nil {
		return
	}
	return pipline.Fire(ctx)
}

func (m *mapper) buildPipeline(pipeline *mapPipeline, from any, toPtr any) error {
	toPtrRV := reflect.ValueOf(toPtr)
	fromRV := reflect.ValueOf(from)
	if fromRV.Kind() == reflect.Slice && toPtrRV.Elem().Kind() == reflect.Slice {
		m.makeSlice(toPtrRV.Elem(), fromRV.Len())
	} else if toPtrRV.Elem().Kind() == reflect.Ptr && fromRV.Kind() == reflect.Ptr {
		m.makeValue(toPtrRV)
	} else if toPtrRV.Elem().Kind() == reflect.Struct ||
		(toPtrRV.Kind() == reflect.Ptr && toPtrRV.Elem().Kind() == reflect.Ptr) {
		m.makeValue(toPtrRV)
	} else {
		return fmt.Errorf("%s: %w", ErrInvalidTo, errors.New("invalid toPtr. to Ptr must be a point of slice|struct"))
	}

	return m.buildPipelineOnFrom(pipeline, from, toPtr)
}

func (m *mapper) makeSlice(sliceRV reflect.Value, len int) {
	sliceRV.Set(reflect.MakeSlice(sliceRV.Type(), len, len))
	for i := 0; i < sliceRV.Len(); i++ {
		m.makeValue(sliceRV.Index(i))
	}
}

func (m *mapper) makeValue(value reflect.Value) {
	indirectValue := value
	for indirectValue.Kind() == reflect.Ptr || indirectValue.Kind() == reflect.Interface {
		if indirectValue.IsNil() {
			indirectValue.Set(reflect.New(indirectValue.Type().Elem()))
		}
		indirectValue = indirectValue.Elem()
	}
}

func (m *mapper) buildPipelineOnFrom(pipeline *mapPipeline, from any, toPtr any) error {
	fromValue, err := valuer.Parse(from, m.cache, m.tagKey)
	if err != nil {
		return err
	}
	toValue, err := valuer.Parse(toPtr, m.cache, m.tagKey)
	if err != nil {
		return err
	}
	fromScroll := fromValue.Scroll()
	toScroll := toValue.Scroll()
	if fromScroll.NodeValueLen() != toScroll.NodeValueLen() {
		return fmt.Errorf("%s: %w", ErrInvalidFromAndToLength, "Length of from and to must be same.")
	}
	return m.buildPipelineOnScroll(pipeline, fromScroll, toScroll)
}

func (m *mapper) buildPipelineOnScroll(pipeline *mapPipeline, fromScroll, toScroll valuer.Scroll) error {
	for i := 0; i < fromScroll.NodeValueLen(); i++ {
		fromNode := fromScroll.NodeValue(i)
		toNode := toScroll.NodeValue(i)
		if err := m.buildPipelineOnNode(pipeline, fromNode, toNode); err != nil {
			return fmt.Errorf("%s: %w", ErrInvalidFromAndToLength, err)
		}
	}
	return nil
}

func (m *mapper) buildPipelineOnNode(pipeline *mapPipeline, fromNode, toNode valuer.NodeValue) error {
	for i := 0; i < fromNode.LeafLen(); i++ {
		fromLeaf := fromNode.LeafValue(i)
		if toLeaf, ok := toNode.FieldValue(fromLeaf.Name()).LeafValue(); ok {
			if _, ok := fromLeaf.Tag(TagBuiltinCascade); ok {
				if err := m.buildPipeline(pipeline, fromLeaf.Value().Interface(), toLeaf.Value().Addr().Interface()); err != nil {
					return nil
				}
			} else {
				fromType, ok := fromLeaf.Tag(TagBuiltinType)
				if !ok {
					return fmt.Errorf("%s: from : %s.%s, to : %s.%s", ErrAbsenceOfType,
						fromNode.Name(), fromLeaf.Name(),
						toNode.Name(), toLeaf.Name())
				}
				toType, ok := toLeaf.Tag(TagBuiltinType)
				if !ok {
					return fmt.Errorf("%s: from : %s.%s , to : %s.%s", ErrAbsenceOfType,
						fromNode.Name(), fromLeaf.Name(),
						toNode.Name(), toLeaf.Name())
				}
				if _, ok := fromLeaf.Tag(TagBuiltinFlat); ok {
					if toLeaf.FieldType().Kind() != reflect.Slice || fromLeaf.FieldType().Kind() != reflect.Slice {
						return fmt.Errorf("%s: from type :%s, to type %s", ErrInvaLidFlatFieldType,
							fromLeaf.FieldType().Kind(), toLeaf.FieldType().Kind())
					}
					fromLeafRV := fromLeaf.Value()
					toLeafRV := toLeaf.Value()
					m.makeSlice(toLeafRV, fromLeafRV.Len())
					for j := 0; j < fromLeafRV.Len(); j++ {
						pipeline.AddMapPair(&MapPair{
							from: &MappingValue{
								typ:   fromType,
								field: fromLeaf,
								val:   fromLeafRV.Index(j),
							},
							to: &MappingValue{
								typ:   toType,
								field: toLeaf,
								val:   toLeafRV.Index(j),
							},
						})
					}
				} else {
					pipeline.AddMapPair(&MapPair{
						from: &MappingValue{
							typ:   fromType,
							field: fromLeaf,
							val:   fromLeaf.Value(),
						},
						to: &MappingValue{
							typ:   toType,
							field: toLeaf,
							val:   toLeaf.Value(),
						},
					})
				}
			}
		}
	}
	return nil
}

func NewMapper(tagKey string) Mapper {
	m := &mapper{
		tagKey: tagKey,
		cache:  &sync.Map{},
	}
	return m
}
