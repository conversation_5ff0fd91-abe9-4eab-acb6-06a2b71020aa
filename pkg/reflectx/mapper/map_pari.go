package mapper

import (
	"peanut/pkg/reflectx/schema"
	"reflect"
)

type MapPair struct {
	from *MappingValue
	to   *MappingValue
}

type MappingValue struct {
	typ   string
	field schema.Field
	val   reflect.Value
}

func (m *MappingValue) Name() string {
	return m.field.Name()
}

func (m *MappingValue) Type() string {
	return m.typ
}

func (m *MappingValue) Tag(key string) (string, bool) {
	return m.field.Tag(key)
}

func (m *MappingValue) Value() reflect.Value {
	return m.val
}
