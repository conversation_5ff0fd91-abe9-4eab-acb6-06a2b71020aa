package mapper

import "context"

type MapLifeCycle interface {
	Pre(ctx context.Context, pairs []*MapPair) (rctx context.Context, persistPairs []*MapPair, err error)
	After(ctx context.Context, pairs []*MapPair) (rctx context.Context, err error)
}

type BasicMapLifeCycle struct {
}

func (b *BasicMapLifeCycle) Pre(ctx context.Context, pairs []*MapPair) (rctx context.Context, persistParis []*MapPair, err error) {
	rctx = ctx
	persistParis = pairs
	return
}
func (b *BasicMapLifeCycle) After(ctx context.Context, pairs []*MapPair) (rctx context.Context, err error) {
	rctx = ctx
	return
}
